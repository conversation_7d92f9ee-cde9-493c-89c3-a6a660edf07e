export const URL = 'https://qingcheng.chat/prod-api'
// export const URL = 'https://crownwwcn.com/prod-api'

export const http = {
  Host: URL,

  //全局统一调用接口的方法
  request: function (options) {
    console.log(options)

    wx.request({
      url: this.Host + options.url,
      method: options.method ? options.method : 'GET',
      header: {
        Accept: 'application/json',
      },
      dataType: 'json',
      data: options.data,
      timeout: options.method ? options.method : 10000, // 设置超时时间
      success: (res) => {
        switch (res.statusCode) {
          case 200:
          case 201:
            const data = res.data
            options.success(data)
            break
          case 401:
            wx.clearStorageSync('token')
            // this.toLogin(); // 根据需要，看是否需要强制跳转登陆页
            break
          case 403:
            wx.showToast({
              title: '无权操作',
              icon: 'none',
            })
            break
          case 404:
            wx.showToast({
              title: '请求地址不存在',
              icon: 'none',
            })
            break
          default:
            wx.showToast({
              title: '出错了～请稍后再试',
              icon: 'none',
            })
        }
      },
      fail: (res) => {
        if (options.fail) {
          options.fail(res)
        }
      },
      complete: (res) => {
        if (options.complete) {
          options.complete(res)
        }
      },
    })
  },
}
