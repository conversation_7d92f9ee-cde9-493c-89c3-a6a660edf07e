.container {
  padding: 8px;
}

.list {
  padding: 8px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.item {
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  text-align: center;
  height: 160px;
}

.item-content {
  padding: 8px;
}

.item-logo {
  width: 100%;
  height: 80px;
  border-radius: 6px 6px 0 0;
}

.item-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 8px;
}

.item-description {
  font-size: 10px;
  color: #888;
}

.animation-fade-in {
  animation: fadeIn 1s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
