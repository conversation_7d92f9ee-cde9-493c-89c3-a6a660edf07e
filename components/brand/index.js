Component({
  data: {
    brands: [
      {
        title: '个人搬迁',
        logo: '1-24121P12S5B1',
        desc: '为个人和家庭提供搬家服务',
        url: '',
        url: '/pages/personal-relocation/personal-relocation',
      },
      {
        title: '安居咨询',
        logo: '1-24121P12U0c1',
        desc: '企业搬迁服务',
        url: '/pages/corporate-relocation/corporate-relocation',
      },
      {
        title: '文档管理',
        logo: '1-24121P12UT01',
        desc: '管理公司数据和信息',
        url: '/pages/document-management/document-management',
      },
      {
        title: '企业办公空间优化',
        logo: '1-24121P12952B0',
        desc: '可持续的工作场所变革服务',
        url: '/pages/office-space-optimization/office-space-optimization',
      },
      {
        title: '合同物流',
        logo: '1-24121P12932408',
        desc: '高端品牌的优质物流',
        url: '/pages/contract-logistics/contract-logistics',
      },
      {
        title: '珍品物流服务',
        logo: '1-24121P12910323',
        desc: '专门的艺术品物流服务',
        url: '/pages/art-logistics/art-logistics',
      },
    ],
  },
  methods: {
    onItemTap: function (event) {
      const url = event.currentTarget.dataset.url
      if (url) {
        wx.navigateTo({
          url,
        })
      }
    },
  },
})
