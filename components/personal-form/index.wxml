<view class="form-container">
  <form bindsubmit="formSubmit">
    <!-- 详细信息 -->
    <view class="form-section">
      <view class="section-title">详细信息</view>

      <!-- 称呼、名字、姓氏 -->
      <view class="form-row">
        <view class="form-item">
          <text class="form-label">称呼<text class="required">*</text></text>
          <view class="external-class" bind:tap="showTitlePicker">
            <view class="picker-container">
              <view class="picker-value">{{titles[selectedTitleIndex]}}</view>
              <view class="picker-arrow">▼</view>
            </view>
          </view>
          <t-picker
            visible="{{titlePickerVisible}}"
            title="选择称呼"
            cancelBtn="取消"
            confirmBtn="确认"
            bindchange="onTitleChange"
            bindpick="onTitlePick"
            bindcancel="hideTitlePicker"
          >
            <t-picker-item options="{{titles}}" />
          </t-picker>
        </view>
        <view class="form-item">
          <text class="form-label">名字<text class="required">*</text></text>
          <t-input value="{{firstName}}" placeholder="请输入名字" bindchange="onFirstNameInput" />
        </view>
      </view>

      <view class="form-row">
        <view class="form-item">
          <text class="form-label">姓氏<text class="required">*</text></text>
          <t-input value="{{lastName}}" placeholder="请输入姓氏" bindchange="onLastNameInput" />
        </view>
        <view class="form-item">
          <text class="form-label">电子邮箱<text class="required">*</text></text>
          <t-input value="{{email}}" placeholder="请输入电子邮箱" bindchange="onEmailInput" type="text" />
        </view>
      </view>

      <view class="form-row">
        <view class="form-item">
          <text class="form-label">电话号码<text class="required">*</text></text>
          <t-input value="{{phone}}" placeholder="请输入电话号码" bindchange="onPhoneInput" type="number" />
        </view>
      </view>

      <!-- 协议和选项 -->
      <view class="form-row">
        <view class="form-item full-width">
          <view class="checkbox-item">
            <t-checkbox
              value="{{privacyPolicyAgreed}}"
              label="确认您已阅读并同意我们的隐私政策"
              bind:change="onPrivacyPolicyChange"
            />
          </view>
          <view class="checkbox-item">
            <t-checkbox
              value="{{marketingEmailsAgreed}}"
              label="接收嘉柏发送的最新消息丶文章和优惠信息邮件"
              bind:change="onMarketingEmailsChange"
            />
          </view>
        </view>
      </view>

      <!-- 之前是否找嘉柏搬迁过 -->
      <view class="form-row">
        <view class="form-item full-width">
          <text class="form-label">之前是否找嘉柏搬迁过？</text>
          <t-radio-group value="{{previousRelocation}}" bind:change="onPreviousRelocationChange">
            <view class="radio-group">
              <view class="radio-item">
                <t-radio value="0" label="否" />
              </view>
              <view class="radio-item">
                <t-radio value="1" label="是" />
              </view>
            </view>
          </t-radio-group>
        </view>
      </view>

      <!-- 是否曾经住过目的地 -->
      <view class="form-row">
        <view class="form-item full-width">
          <text class="form-label">你曾经住过这目的地吗?</text>
          <t-radio-group value="{{livedAtDestination}}" bind:change="onLivedAtDestinationChange">
            <view class="radio-group">
              <view class="radio-item">
                <t-radio value="0" label="否" />
              </view>
              <view class="radio-item">
                <t-radio value="1" label="是" />
              </view>
            </view>
          </t-radio-group>
        </view>
      </view>
    </view>

    <!-- 搬迁出发地 -->
    <view class="form-section">
      <view class="section-title">搬迁出发地</view>
      <view class="form-row">
        <view class="form-item">
          <text class="form-label">选择地区<text class="required">*</text></text>
          <view class="external-class" bind:tap="showOriginRegionPicker">
            <view class="picker-container">
              <view class="picker-value">{{regions[originRegionIndex]}}</view>
              <view class="picker-arrow">▼</view>
            </view>
          </view>
          <t-picker
            visible="{{originRegionPickerVisible}}"
            title="选择地区"
            cancelBtn="取消"
            confirmBtn="确认"
            bindchange="onOriginRegionChange"
            bindpick="onOriginRegionPick"
            bindcancel="hideOriginRegionPicker"
          >
            <t-picker-item options="{{regions}}" />
          </t-picker>
        </view>
        <view class="form-item">
          <text class="form-label">具体地址<text class="required">*</text></text>
          <t-input value="{{originAddress}}" placeholder="请输入详细地址" bindchange="onOriginAddressInput" />
        </view>
      </view>
    </view>

    <!-- 搬迁目的地 -->
    <view class="form-section">
      <view class="section-title">搬迁目的地</view>
      <view class="form-row">
        <view class="form-item">
          <text class="form-label">选择地区<text class="required">*</text></text>
          <view class="external-class" bind:tap="showDestinationRegionPicker">
            <view class="picker-container">
              <view class="picker-value">{{regions[destinationRegionIndex]}}</view>
              <view class="picker-arrow">▼</view>
            </view>
          </view>
          <t-picker
            visible="{{destinationRegionPickerVisible}}"
            title="选择地区"
            cancelBtn="取消"
            confirmBtn="确认"
            bindchange="onDestinationRegionChange"
            bindpick="onDestinationRegionPick"
            bindcancel="hideDestinationRegionPicker"
          >
            <t-picker-item options="{{regions}}" />
          </t-picker>
        </view>
        <view class="form-item">
          <text class="form-label">具体地址<text class="required">*</text></text>
          <t-input value="{{destinationAddress}}" placeholder="请输入详细地址" bindchange="onDestinationAddressInput" />
        </view>
      </view>
    </view>

    <!-- 搬迁时间 -->
    <view class="form-section">
      <view class="section-title">搬迁时间</view>

      <!-- 时间选择方式切换 -->
      <t-tabs value="{{isTimePeriod ? '1' : '0'}}" bind:change="onTimeTabsChange" theme="tag" space-evenly="{{false}}">
        <t-tab-panel label="具体日期" value="0" />
        <t-tab-panel label="时间段" value="1" />
      </t-tabs>

      <!-- 日期选择器 -->
      <view class="form-row" wx:if="{{!isTimePeriod}}">
        <view class="form-item full-width">
          <text class="form-label">选择日期<text class="required">*</text></text>
          <view class="external-class" bind:tap="showDatePicker">
            <view class="picker-container">
              <view class="picker-value">{{moveDate}}</view>
              <view class="picker-arrow">▼</view>
            </view>
          </view>
          <t-date-time-picker
            visible="{{datePickerVisible}}"
            mode="date"
            value="{{moveDate}}"
            title="选择日期"
            format="YYYY-MM-DD"
            start="{{today}}"
            cancelBtn="取消"
            confirmBtn="确认"
            bindchange="onDateChange"
            bindpick="onDatePick"
            bindcancel="hideDatePicker"
          />
        </view>
      </view>

      <!-- 时间段选择器 -->
      <view class="form-row" wx:if="{{isTimePeriod}}">
        <view class="form-item full-width">
          <text class="form-label">选择时间段<text class="required">*</text></text>
          <view class="external-class" bind:tap="showTimePeriodPicker">
            <view class="picker-container">
              <view class="picker-value">{{timePeriods[selectedTimePeriod]}}</view>
              <view class="picker-arrow">▼</view>
            </view>
          </view>
          <t-picker
            visible="{{timePeriodPickerVisible}}"
            title="选择时间段"
            cancelBtn="取消"
            confirmBtn="确认"
            bindchange="onTimePeriodChange"
            bindpick="onTimePeriodPick"
            bindcancel="hideTimePeriodPicker"
          >
            <t-picker-item options="{{timePeriods}}" />
          </t-picker>
        </view>
      </view>
    </view>

    <!-- 您要搬迁什么 -->
    <view class="form-section">
      <view class="section-title">您要搬迁什么</view>

      <!-- 测量方式选择器 -->
      <t-tabs
        value="{{measurementMethod.toString()}}"
        bind:change="onMeasurementTabsChange"
        theme="tag"
        space-evenly="{{false}}"
      >
        <t-tab-panel label="规模" value="0" />
        <t-tab-panel label="立方米" value="1" />
        <t-tab-panel label="重量" value="2" />
        <t-tab-panel label="集装箱" value="3" />
      </t-tabs>

      <!-- 规模选择器 -->
      <view class="form-row" wx:if="{{measurementMethod === 0}}">
        <view class="form-item full-width">
          <text class="form-label">选择规模<text class="required">*</text></text>
          <view class="external-class" bind:tap="showSizeOptionPicker">
            <view class="picker-container">
              <view class="picker-value">{{sizeOptions[selectedSizeOption]}}</view>
              <view class="picker-arrow">▼</view>
            </view>
          </view>
          <t-picker
            visible="{{sizeOptionPickerVisible}}"
            title="选择规模"
            cancelBtn="取消"
            confirmBtn="确认"
            bindchange="onSizeOptionChange"
            bindpick="onSizeOptionPick"
            bindcancel="hideSizeOptionPicker"
          >
            <t-picker-item options="{{sizeOptions}}" />
          </t-picker>
        </view>
      </view>

      <!-- 立方米输入 -->
      <view class="form-row" wx:if="{{measurementMethod === 1}}">
        <view class="form-item full-width">
          <text class="form-label">立方米数量<text class="required">*</text></text>
          <t-input
            value="{{cubicMeters}}"
            type="digit"
            placeholder="请输入数量"
            suffix="m³"
            align="right"
            bindchange="onCubicMetersInput"
          />
        </view>
      </view>

      <!-- 重量输入 -->
      <view class="form-row" wx:if="{{measurementMethod === 2}}">
        <view class="form-item">
          <text class="form-label">重量<text class="required">*</text></text>
          <t-input value="{{weight}}" type="digit" placeholder="请输入重量" align="right" bindchange="onWeightInput" />
        </view>
        <view class="form-item">
          <text class="form-label">单位<text class="required">*</text></text>
          <view class="external-class" bind:tap="showWeightUnitPicker">
            <view class="picker-container">
              <view class="picker-value">{{weightUnits[selectedWeightUnit]}}</view>
              <view class="picker-arrow">▼</view>
            </view>
          </view>
          <t-picker
            visible="{{weightUnitPickerVisible}}"
            title="选择单位"
            cancelBtn="取消"
            confirmBtn="确认"
            bindchange="onWeightUnitChange"
            bindpick="onWeightUnitPick"
            bindcancel="hideWeightUnitPicker"
          >
            <t-picker-item options="{{weightUnits}}" />
          </t-picker>
        </view>
      </view>

      <!-- 集装箱尺寸选择 -->
      <view class="form-row" wx:if="{{measurementMethod === 3}}">
        <view class="form-item full-width">
          <text class="form-label">集装箱尺寸<text class="required">*</text></text>
          <t-radio-group value="{{selectedContainerSize.toString()}}" bind:change="onContainerSizeChange">
            <view class="radio-group">
              <view wx:for="{{containerSizes}}" wx:key="index" class="radio-item">
                <t-radio value="{{index.toString()}}" label="{{item}}" />
              </view>
            </view>
          </t-radio-group>
        </view>
      </view>
    </view>

    <!-- 谁要搬迁 -->
    <view class="form-section">
      <view class="section-title">谁要搬迁</view>

      <!-- 人员类型选择器 -->
      <view class="form-row">
        <view class="form-item">
          <text class="form-label">人员类型<text class="required">*</text></text>
          <view class="external-class" bind:tap="showPersonTypePicker">
            <view class="picker-container">
              <view class="picker-value">{{personTypes[selectedPersonType]}}</view>
              <view class="picker-arrow">▼</view>
            </view>
          </view>
          <t-picker
            visible="{{personTypePickerVisible}}"
            title="选择人员类型"
            cancelBtn="取消"
            confirmBtn="确认"
            bindchange="onPersonTypeChange"
            bindpick="onPersonTypePick"
            bindcancel="hidePersonTypePicker"
          >
            <t-picker-item options="{{personTypes}}" />
          </t-picker>
        </view>
      </view>

      <!-- 人数输入 -->
      <view class="form-row">
        <view class="form-item">
          <text class="form-label">成人数量<text class="required">*</text></text>
          <t-stepper value="{{adultCount}}" min="1" theme="filled" bindchange="onAdultCountChange" />
        </view>
        <view class="form-item">
          <text class="form-label">儿童数量</text>
          <t-stepper value="{{childCount}}" min="0" theme="filled" bindchange="onChildCountChange" />
        </view>
      </view>
    </view>

    <!-- 附加服务 -->
    <view class="form-section">
      <view class="section-title">附加服务</view>
      <view class="form-row">
        <view class="form-item full-width">
          <text class="form-label">选择您需要的附加服务</text>
          <view class="additional-services">
            <view wx:for="{{additionalServices}}" wx:key="id" class="service-item">
              <t-checkbox
                value="{{item.checked}}"
                label="{{item.title}}"
                data-id="{{item.id}}"
                bind:change="onAdditionalServiceChange"
              />
              <view class="service-description">{{item.description}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="form-actions">
      <t-button theme="primary" size="large" shape="round" block bind:tap="formSubmit">提交</t-button>
    </view>
  </form>
</view>
