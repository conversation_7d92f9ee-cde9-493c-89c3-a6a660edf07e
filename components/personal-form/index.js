Component({
  data: {
    // 地区选择器数据
    regions: ['北京市', '上海市', '广州市', '深圳市', '天津市', '重庆市', '武汉市', '成都市', '杭州市', '南京市'],
    originRegionIndex: 0,
    destinationRegionIndex: 0,
    originAddress: '',
    destinationAddress: '',
    // Picker 可见性控制
    originRegionPickerVisible: false,
    destinationRegionPickerVisible: false,
    // 日期选择
    moveDate: '',
    today: '',
    datePickerVisible: false,
    // 时间段选择
    isTimePeriod: false, // 是否选择时间段而不是具体日期
    timePeriods: ['上午 (8:00-12:00)', '下午 (12:00-18:00)', '晚上 (18:00-22:00)'],
    selectedTimePeriod: 0,
    timePeriodPickerVisible: false,
    // 搬迁物品测量方式
    measurementMethod: 0, // 0: 规模, 1: 立方米, 2: 重量, 3: 集装箱尺寸
    // 规模选项
    sizeOptions: ['小型 (1-2人公寓)', '中型 (2-3人住宅)', '大型 (4人以上住宅)', '办公室', '商业空间'],
    selectedSizeOption: 0,
    sizeOptionPickerVisible: false,
    // 立方米
    cubicMeters: '',
    // 重量
    weight: '',
    weightUnits: ['公斤', '磅'],
    selectedWeightUnit: 0,
    weightUnitPickerVisible: false,
    // 集装箱尺寸
    containerSizes: ['20英尺标准箱', '40英尺标准箱', '40英尺高箱', '45英尺高箱'],
    selectedContainerSize: 0,
    // 搬迁人员选项
    personTypes: ['本人', '同事'],
    selectedPersonType: 0,
    personTypePickerVisible: false,
    adultCount: '1', // 默认成人数量为1
    childCount: '0', // 默认儿童数量为0
    // 附加服务选项
    additionalServices: [
      {
        id: 'housing',
        title: '住宅搜寻',
        description: '每个目的地都会提供各种住宿方式，以适合每个人的需求丶生活方式和预算。',
        checked: false,
      },
      {
        id: 'immigration',
        title: '入境服务',
        description:
          '如果您想要跟上各个国家的最新外籍人口控制法律法规和外派员工状况的最新消息, 您需要一个专业并乐此不疲的合作伙伴。',
        checked: false,
      },
      {
        id: 'petRelocation',
        title: 'Pet Relocations',
        description:
          'Your pets have specific needs, just like you do. Our pet relocation services treat your pet like a member of your family, making things comfortable for them and simple for you.',
        checked: false,
      },
    ],
    // 详细信息
    // 称呼选项
    titles: ['先生', '女士', '博士', '教授'],
    selectedTitleIndex: 0,
    titlePickerVisible: false,
    // 个人信息
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    // 协议和选项
    privacyPolicyAgreed: false,
    marketingEmailsAgreed: false,
    // 之前是否找嘉柏搬迁过
    previousRelocation: '0', // 0: 否, 1: 是
    // 是否曾经住过目的地
    livedAtDestination: '0', // 0: 否, 1: 是
  },

  lifetimes: {
    attached() {
      // 设置今天的日期作为默认值
      const today = new Date()
      const year = today.getFullYear()
      const month = String(today.getMonth() + 1).padStart(2, '0')
      const day = String(today.getDate()).padStart(2, '0')
      const formattedDate = `${year}-${month}-${day}`

      this.setData({
        moveDate: formattedDate,
        today: formattedDate,
      })
    },
  },

  methods: {
    // 出发地区域选择器相关方法
    showOriginRegionPicker() {
      this.setData({
        originRegionPickerVisible: true,
      })
    },

    hideOriginRegionPicker() {
      this.setData({
        originRegionPickerVisible: false,
      })
    },

    onOriginRegionPick(e) {
      // 用户滑动选择时的临时选择，不做处理
    },

    onOriginRegionChange(e) {
      this.setData({
        originRegionIndex: e.detail.value[0],
        originRegionPickerVisible: false,
      })
    },

    // 处理出发地地址输入变化
    onOriginAddressInput(e) {
      this.setData({
        originAddress: e.detail.value,
      })
    },

    // 目的地区域选择器相关方法
    showDestinationRegionPicker() {
      this.setData({
        destinationRegionPickerVisible: true,
      })
    },

    hideDestinationRegionPicker() {
      this.setData({
        destinationRegionPickerVisible: false,
      })
    },

    onDestinationRegionPick(e) {
      // 用户滑动选择时的临时选择，不做处理
    },

    onDestinationRegionChange(e) {
      this.setData({
        destinationRegionIndex: e.detail.value[0],
        destinationRegionPickerVisible: false,
      })
    },

    // 处理目的地地址输入变化
    onDestinationAddressInput(e) {
      this.setData({
        destinationAddress: e.detail.value,
      })
    },

    // 日期选择器相关方法
    showDatePicker() {
      this.setData({
        datePickerVisible: true,
      })
    },

    hideDatePicker() {
      this.setData({
        datePickerVisible: false,
      })
    },

    onDatePick(e) {
      // 用户滑动选择时的临时选择，不做处理
    },

    onDateChange(e) {
      this.setData({
        moveDate: e.detail.value,
        datePickerVisible: false,
      })
    },

    // 处理时间选择方式切换
    onTimeTabsChange(e) {
      this.setData({
        isTimePeriod: e.detail.value === '1',
      })
    },

    // 时间段选择器相关方法
    showTimePeriodPicker() {
      this.setData({
        timePeriodPickerVisible: true,
      })
    },

    hideTimePeriodPicker() {
      this.setData({
        timePeriodPickerVisible: false,
      })
    },

    onTimePeriodPick(e) {
      // 用户滑动选择时的临时选择，不做处理
    },

    onTimePeriodChange(e) {
      this.setData({
        selectedTimePeriod: e.detail.value[0],
        timePeriodPickerVisible: false,
      })
    },

    // 处理测量方式切换
    onMeasurementTabsChange(e) {
      this.setData({
        measurementMethod: parseInt(e.detail.value),
      })
    },

    // 规模选择器相关方法
    showSizeOptionPicker() {
      this.setData({
        sizeOptionPickerVisible: true,
      })
    },

    hideSizeOptionPicker() {
      this.setData({
        sizeOptionPickerVisible: false,
      })
    },

    onSizeOptionPick(e) {
      // 用户滑动选择时的临时选择，不做处理
    },

    onSizeOptionChange(e) {
      this.setData({
        selectedSizeOption: e.detail.value[0],
        sizeOptionPickerVisible: false,
      })
    },

    // 处理立方米输入变化
    onCubicMetersInput(e) {
      this.setData({
        cubicMeters: e.detail.value,
      })
    },

    // 处理重量输入变化
    onWeightInput(e) {
      this.setData({
        weight: e.detail.value,
      })
    },

    // 重量单位选择器相关方法
    showWeightUnitPicker() {
      this.setData({
        weightUnitPickerVisible: true,
      })
    },

    hideWeightUnitPicker() {
      this.setData({
        weightUnitPickerVisible: false,
      })
    },

    onWeightUnitPick(e) {
      // 用户滑动选择时的临时选择，不做处理
    },

    onWeightUnitChange(e) {
      this.setData({
        selectedWeightUnit: e.detail.value[0],
        weightUnitPickerVisible: false,
      })
    },

    // 处理集装箱尺寸选择变化
    onContainerSizeChange(e) {
      this.setData({
        selectedContainerSize: parseInt(e.detail.value),
      })
    },

    // 人员类型选择器相关方法
    showPersonTypePicker() {
      this.setData({
        personTypePickerVisible: true,
      })
    },

    hidePersonTypePicker() {
      this.setData({
        personTypePickerVisible: false,
      })
    },

    onPersonTypePick(e) {
      // 用户滑动选择时的临时选择，不做处理
    },

    onPersonTypeChange(e) {
      this.setData({
        selectedPersonType: e.detail.value[0],
        personTypePickerVisible: false,
      })
    },

    // 处理成人数量变化 (TDesign Stepper)
    onAdultCountChange(e) {
      this.setData({
        adultCount: e.detail.value,
      })
    },

    // 处理儿童数量变化 (TDesign Stepper)
    onChildCountChange(e) {
      this.setData({
        childCount: e.detail.value,
      })
    },

    // 处理附加服务选择变化
    onAdditionalServiceChange(e) {
      const serviceId = e.currentTarget.dataset.id
      const checked = e.detail.checked

      // 找到对应的服务并更新其选中状态
      const additionalServices = this.data.additionalServices.map((service) => {
        if (service.id === serviceId) {
          return { ...service, checked: checked }
        }
        return service
      })

      this.setData({
        additionalServices: additionalServices,
      })
    },

    // 称呼选择器相关方法
    showTitlePicker() {
      this.setData({
        titlePickerVisible: true,
      })
    },

    hideTitlePicker() {
      this.setData({
        titlePickerVisible: false,
      })
    },

    onTitlePick(e) {
      // 用户滑动选择时的临时选择，不做处理
    },

    onTitleChange(e) {
      this.setData({
        selectedTitleIndex: e.detail.value[0],
        titlePickerVisible: false,
      })
    },

    // 处理名字输入变化
    onFirstNameInput(e) {
      this.setData({
        firstName: e.detail.value,
      })
    },

    // 处理姓氏输入变化
    onLastNameInput(e) {
      this.setData({
        lastName: e.detail.value,
      })
    },

    // 处理电子邮箱输入变化
    onEmailInput(e) {
      this.setData({
        email: e.detail.value,
      })
    },

    // 处理电话号码输入变化
    onPhoneInput(e) {
      this.setData({
        phone: e.detail.value,
      })
    },

    // 处理隐私政策同意变化
    onPrivacyPolicyChange(e) {
      this.setData({
        privacyPolicyAgreed: e.detail.checked,
      })
    },

    // 处理营销邮件同意变化
    onMarketingEmailsChange(e) {
      this.setData({
        marketingEmailsAgreed: e.detail.checked,
      })
    },

    // 处理之前是否找嘉柏搬迁过变化
    onPreviousRelocationChange(e) {
      this.setData({
        previousRelocation: e.detail.value,
      })
    },

    // 处理是否曾经住过目的地变化
    onLivedAtDestinationChange(e) {
      this.setData({
        livedAtDestination: e.detail.value,
      })
    },

    // 表单提交
    formSubmit() {
      // 获取表单数据
      const formData = {
        originAddress: this.data.originAddress || '',
        destinationAddress: this.data.destinationAddress || '',
      }

      // 验证表单数据
      if (!formData.originAddress || !formData.destinationAddress) {
        wx.showToast({
          title: '请填写地址信息',
          icon: 'none',
          duration: 2000,
        })
        return
      }

      // 根据测量方式验证相应的字段
      if (this.data.measurementMethod === 1 && !this.data.cubicMeters) {
        wx.showToast({
          title: '请输入立方米数量',
          icon: 'none',
          duration: 2000,
        })
        return
      }

      if (this.data.measurementMethod === 2 && !this.data.weight) {
        wx.showToast({
          title: '请输入重量',
          icon: 'none',
          duration: 2000,
        })
        return
      }

      // 验证成人数量
      if (!this.data.adultCount || parseInt(this.data.adultCount) < 1) {
        wx.showToast({
          title: '成人数量至少为1',
          icon: 'none',
          duration: 2000,
        })
        return
      }

      // 验证详细信息
      if (!this.data.firstName) {
        wx.showToast({
          title: '请输入名字',
          icon: 'none',
          duration: 2000,
        })
        return
      }

      if (!this.data.lastName) {
        wx.showToast({
          title: '请输入姓氏',
          icon: 'none',
          duration: 2000,
        })
        return
      }

      if (!this.data.email) {
        wx.showToast({
          title: '请输入电子邮箱',
          icon: 'none',
          duration: 2000,
        })
        return
      }

      if (!this.data.phone) {
        wx.showToast({
          title: '请输入电话号码',
          icon: 'none',
          duration: 2000,
        })
        return
      }

      // 验证隐私政策同意
      if (!this.data.privacyPolicyAgreed) {
        wx.showToast({
          title: '请同意隐私政策',
          icon: 'none',
          duration: 2000,
        })
        return
      }

      // 构建提交数据
      const submitData = {
        originRegion: this.data.regions[this.data.originRegionIndex],
        originAddress: formData.originAddress,
        destinationRegion: this.data.regions[this.data.destinationRegionIndex],
        destinationAddress: formData.destinationAddress,
        isTimePeriod: this.data.isTimePeriod,
        moveDate: this.data.isTimePeriod ? '' : this.data.moveDate,
        timePeriod: this.data.isTimePeriod ? this.data.timePeriods[this.data.selectedTimePeriod] : '',
        measurementMethod: this.data.measurementMethod,
        // 根据测量方式添加相应的数据
        sizeOption: this.data.measurementMethod === 0 ? this.data.sizeOptions[this.data.selectedSizeOption] : '',
        cubicMeters: this.data.measurementMethod === 1 ? this.data.cubicMeters : '',
        weight: this.data.measurementMethod === 2 ? this.data.weight : '',
        weightUnit: this.data.measurementMethod === 2 ? this.data.weightUnits[this.data.selectedWeightUnit] : '',
        containerSize:
          this.data.measurementMethod === 3 ? this.data.containerSizes[this.data.selectedContainerSize] : '',
        personType: this.data.personTypes[this.data.selectedPersonType],
        adultCount: this.data.adultCount,
        childCount: this.data.childCount,
        // 添加选中的附加服务
        additionalServices: this.data.additionalServices
          .filter((service) => service.checked)
          .map((service) => ({
            id: service.id,
            title: service.title,
          })),
        // 添加详细信息
        title: this.data.titles[this.data.selectedTitleIndex],
        firstName: this.data.firstName,
        lastName: this.data.lastName,
        email: this.data.email,
        phone: this.data.phone,
        privacyPolicyAgreed: this.data.privacyPolicyAgreed,
        marketingEmailsAgreed: this.data.marketingEmailsAgreed,
        previousRelocation: this.data.previousRelocation === '1', // 转换为布尔值
        livedAtDestination: this.data.livedAtDestination === '1', // 转换为布尔值
      }

      // 触发自定义事件，将数据传递给父组件
      this.triggerEvent('submit', submitData)

      // 显示提交成功提示
      wx.showToast({
        title: '提交成功',
        icon: 'success',
        duration: 2000,
      })
    },
  },
})
