/* 表单容器 */
.form-container {
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
}

/* 表单区块 */
.form-section {
  margin-bottom: 30rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #cc0000;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
  border-left: 8rpx solid #cc0000;
}

/* 表单行 */
.form-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

/* 表单项 */
.form-item {
  flex: 1;
  min-width: 300rpx;
  padding: 0 10rpx;
  margin-bottom: 20rpx;
}

.form-item.full-width {
  width: 100%;
  flex-basis: 100%;
}

/* 表单标签 */
.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.required {
  color: #cc0000;
  margin-left: 4rpx;
}

/* 表单输入框 */
.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
}

/* 选择器样式 */
.external-class {
  width: 100%;
}

.picker-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.picker-value {
  font-size: 28rpx;
  color: #333;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 时间选择器切换按钮 */
.time-selector-toggle {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.toggle-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}

.toggle-buttons {
  display: flex;
  border: 1rpx solid #cc0000;
  border-radius: 8rpx;
  overflow: hidden;
}

.toggle-button {
  padding: 10rpx 30rpx;
  font-size: 26rpx;
  background-color: #fff;
  color: #cc0000;
  text-align: center;
  transition: all 0.3s;
}

.toggle-button.active {
  background-color: #cc0000;
  color: #fff;
}

/* 测量方式选择器 */
.measurement-toggle {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.measurement-buttons {
  display: flex;
  flex-wrap: wrap;
  border: 1rpx solid #cc0000;
  border-radius: 8rpx;
  overflow: hidden;
}

.measurement-button {
  padding: 10rpx 20rpx;
  font-size: 26rpx;
  background-color: #fff;
  color: #cc0000;
  text-align: center;
  transition: all 0.3s;
}

.measurement-button.active {
  background-color: #cc0000;
  color: #fff;
}

/* 带单位的输入框 */
.input-with-unit {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input.with-unit {
  padding-right: 60rpx;
}

.input-unit {
  position: absolute;
  right: 20rpx;
  font-size: 28rpx;
  color: #666;
}

/* 数字输入框 */
.number-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input.number-input {
  padding-right: 80rpx;
  text-align: center;
}

.number-controls {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-left: 1rpx solid #ddd;
}

.number-control {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80rpx;
  height: 50%;
  font-size: 28rpx;
  color: #666;
  background-color: #f5f5f5;
}

.number-control:first-child {
  border-bottom: 1rpx solid #ddd;
}

/* 复选框组 */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
}

.checkbox-item {
  display: flex;
  align-items: flex-start;
  margin-right: 30rpx;
  margin-bottom: 20rpx;
  padding: 10rpx 0;
}

.checkbox-label {
  font-size: 28rpx;
  margin-left: 8rpx;
}

/* 详细信息样式 */
.agreement-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-left: 60rpx;
  margin-top: 6rpx;
}

/* 单选框组 */
.radio-group {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 20rpx;
}

.radio-label {
  font-size: 28rpx;
  margin-left: 8rpx;
}

/* 附加服务样式 */
.additional-services {
  margin-top: 20rpx;
}

.service-item {
  margin-bottom: 24rpx;
  padding: 16rpx;
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.service-description {
  margin-top: 10rpx;
  font-size: 26rpx;
  color: #666;
  padding-left: 60rpx;
  line-height: 1.5;
}

/* 提交按钮 */
.form-actions {
  margin-top: 40rpx;
  display: flex;
  justify-content: center;
}

.submit-button {
  width: 80%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #cc0000;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 44rpx;
  box-shadow: 0 6rpx 10rpx rgba(204, 0, 0, 0.2);
}
