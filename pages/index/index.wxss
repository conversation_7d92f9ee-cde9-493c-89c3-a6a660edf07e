/**index.wxss**/
.scrollarea {
  margin-top: -1px;
  min-height: 100vh;
  background-color: #f2f5fb;
}
.banner {
  position: relative;
  height: 300rpx;
  width: 100%;
}

.banner-logo {
  width: 200rpx;
  height: 100rpx;
}

.banner-img {
  width: 100%;
  height: 100%;
}

.banner-text {
  position: absolute;
  top: 60%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.banner-title {
  font-size: 36rpx;
}

.banner-subtitle {
  font-size: 18rpx;
}

.brand {
  margin-top: 24px;
}

.footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 24px;
  margin-bottom: 24px;
  padding: 20px;
}

.qrcode {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 16rpx;
}

.icp-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

.activity {
  margin-top: 24px;
  padding: 0 16px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.list-title {
  display: flex;
  justify-content: center;
  color: #c00;
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 12px;
}

.activity-list {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 16px;
  position: relative;
  background-color: #fff;
}

.activity-item:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 16px;
  right: 16px;
  height: 1px;
  background-color: #f0f0f0;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.activity-time {
  font-size: 12px;
  color: #999;
  margin-left: 12px;
}
