// index.js
import { http } from '../../utils/http.js'

Page({
  data: {
    recentActivities: [],
    showLoginMask: false,
  },

  onLoad() {},

  loadUserInfo() {
    wx.showLoading({
      title: '加载中...',
      mask: true,
    })

    http.request({
      url: '/api/wx/activity/info/list',
      success: (res) => {
        const list = res.rows.map((item) => ({
          id: item.activityId,
          title: item.activityName,
          time: item.activityDate,
        }))
        this.setData({
          recentActivities: list,
        })
        wx.hideLoading()
      },
      complete: () => {
        wx.hideLoading()
      },
    })
  },

  handleActivityTap(event) {
    const activityId = event.currentTarget.dataset.id
    console.log('Tapped activity with ID:', activityId)

    const scene = encodeURIComponent(`activityId=${activityId}`)
    wx.navigateTo({
      url: `/pages/activity/activity?scene=${scene}`,
    })
  },
})
