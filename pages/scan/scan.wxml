<view class="container">
  <!-- Floating <PERSON>an Button -->
  <view class="scan-button-container">
    <button class="scan-button" bindtap="scanCode">扫码</button>
  </view>

  <!-- User Information Card -->
  <view class="user-container" wx:if="{{userInfo}}">
    <view class="user-card">
      <view class="card-content">
        <view class="info-section">
          <view class="section-title">基本信息</view>
          <view class="info-row">
            <view class="info-item">
              <text class="label">姓名</text>
              <text class="value">{{userInfo.name || '未设置'}}</text>
            </view>
            <view class="info-item">
              <text class="label">手机号码</text>
              <text class="value">{{userInfo.phone || '未设置'}}</text>
            </view>
            <view class="info-item">
              <text class="label">邮箱</text>
              <text class="value">{{userInfo.email || '未设置'}}</text>
            </view>
          </view>
        </view>

        <view class="info-section">
          <view class="section-title">工作信息</view>
          <view class="info-row">
            <view class="info-item full-width">
              <text class="label">公司</text>
              <text class="value">{{userInfo.company || '未设置'}}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item full-width">
              <text class="label">职位</text>
              <text class="value">{{userInfo.title || '未设置'}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- Check-in Button -->
      <view class="check-in-section">
        <button
          class="check-in-button {{isCheckedIn ? 'checked' : ''}}"
          bindtap="handleCheckIn"
          disabled="{{isCheckedIn}}"
        >
          {{isCheckedIn ? '已签到' : '确认签到'}}
        </button>
      </view>
    </view>
  </view>

  <!-- Empty State -->
  <view class="empty-state" wx:else>
    <text class="empty-text">请点击扫码按钮扫描用户二维码</text>
  </view>
</view>
