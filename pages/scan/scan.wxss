.container {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  padding-bottom: 80px;
}

/* <PERSON><PERSON> */
.scan-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.98);
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);
  z-index: 100;
  display: flex;
  justify-content: center;
  backdrop-filter: blur(10px);
  height: 80px;
}

.scan-button {
  width: 80%;
  height: 88rpx;
  background: linear-gradient(135deg, #cc0000, #e60000);
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: 0 6rpx 20rpx rgba(204, 0, 0, 0.3);
  margin: 24rpx 40rpx 40rpx;
  letter-spacing: 4rpx;
}

.scan-button::after {
  content: '';
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.3) 10%, transparent 10.01%);
  background-repeat: no-repeat;
  background-position: 50%;
  transform: scale(10, 10);
  opacity: 0;
  transition: transform 0.5s, opacity 1s;
}

.scan-button:active::after {
  transform: scale(0, 0);
  opacity: 0.3;
  transition: 0s;
}

.scan-icon {
  width: 48rpx;
  height: 48rpx;
}

.scan-text {
  font-size: 24rpx;
  color: #ffffff;
  margin-top: 4rpx;
}

/* User Card Styles */
.user-container {
  width: 100%;
  padding: 20rpx;
}

.user-card {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin: 20rpx 0;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(204, 153, 51, 0.1);
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 40rpx;
}

.info-section {
  background-color: #ffffff;
  border-radius: 16rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #cc9933;
  margin-bottom: 24rpx;
  padding-left: 20rpx;
  border-left: 8rpx solid #cc9933;
}

.info-row {
  display: flex;
  flex-wrap: wrap;
  gap: 30rpx;
  margin-bottom: 20rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  flex: 1;
  min-width: 300rpx;
  background-color: #f9f9f9;
  padding: 20rpx;
  border-radius: 12rpx;
  border: 1px solid #f0f0f0;
}

.info-item.full-width {
  flex: 2;
  min-width: 100%;
}

.label {
  font-size: 26rpx;
  color: #666666;
  display: block;
  margin-bottom: 8rpx;
}

.value {
  font-size: 28rpx;
  color: #333333;
  display: block;
  font-weight: 500;
}

/* Check-in Button Styles */
.check-in-section {
  margin-top: 40rpx;
  padding: 0 20rpx;
}

.check-in-button {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #cc9933, #e6b800);
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: 0 6rpx 20rpx rgba(204, 153, 51, 0.3);
  letter-spacing: 4rpx;
}

.check-in-button.checked {
  background: linear-gradient(135deg, #999999, #cccccc);
  box-shadow: 0 6rpx 20rpx rgba(153, 153, 153, 0.3);
}

/* Empty State Styles */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
  margin-top: 20rpx;
}
