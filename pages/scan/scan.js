import { http } from '../../utils/http.js'

Page({
  data: {
    userInfo: null,
    isCheckedIn: false,
    activityId: '',
    openId: '',
  },

  onLoad() {
    // Initialize page
  },

  loadUserInfo({ activityId, openId }) {
    http.request({
      url: `/api/wx/activity/${activityId}/participant/${openId}`,
      success: (res) => {
        if (res?.code === 200 && res.data) {
          this.setData({
            userInfo: res.data,
            isCheckedIn: res.data?.checkIn === 'Y',
          })
        } else {
          wx.showToast({
            title: '加载用户信息失败',
            icon: 'error',
          })
        }
      },
    })
  },

  // Handle QR code scanning
  scanCode() {
    wx.scanCode({
      success: (res) => {
        // Assuming the QR code contains a JSON string with user info
        try {
          const qrRes = JSON.parse(res.result)
          this.setData({
            activityId: qrRes.activityId,
            openId: qrRes.openId,
          })
          this.loadUserInfo(qrRes)
        } catch (error) {
          wx.showToast({
            title: '无效的二维码',
            icon: 'error',
          })
        }
      },
    })
  },

  // Handle check-in
  handleCheckIn() {
    if (!this.data.userInfo) return

    wx.showLoading({
      title: '处理中...',
    })

    const { activityId, openId } = this.data

    http.request({
      url: `/api/wx/activity/${activityId}/participant/${openId}/checkIn`,
      method: 'POST',
      success: (res) => {
        if (res.code === 200) {
          this.setData({
            isCheckedIn: true,
          })
          wx.showToast({
            title: '签到成功',
            icon: 'success',
          })
        } else {
          wx.showToast({
            title: res.msg || '签到失败',
            icon: 'error',
          })
        }
        wx.hideLoading()
      },
      fail: () => {
        wx.hideLoading()
        wx.showToast({
          title: '签到失败',
          icon: 'error',
        })
      },
    })
  },
})
