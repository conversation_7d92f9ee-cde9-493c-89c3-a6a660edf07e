// pages/personal-relocation/personal-relocation.js
import { http } from '../../utils/http.js'

Page({
  data: {
    pageTitle: '个人搬迁',
    pageDescription: '为个人和家庭提供搬家服务',
    services: [
      {
        title: '国内搬家',
        description: '提供城市内和城市间的个人搬家服务',
        icon: 'home',
      },
      {
        title: '国际搬家',
        description: '为跨国迁移提供全方位的搬家解决方案',
        icon: 'global',
      },
      {
        title: '贵重物品搬运',
        description: '专业处理贵重和易碎物品的搬运',
        icon: 'diamond',
      },
      {
        title: '打包服务',
        description: '提供专业的物品打包和拆箱服务',
        icon: 'box',
      },
    ],
    showFormPopup: false,
    capture: '',
    uuid: '',
    relocationForm: {
      originCountry: '中国',
      originCity: 'shanghai',
      destinationCountry: '中国',
      destinationCity: 'wuhai',
      moveDate: '2025/05/08',
      unknownDate: false,
    },
  },

  onLoad() {
    this.refreshCaptcha()
  },

  refreshCaptcha() {
    http.request({
      url: `/api/wx/captchaImage`,
      success: (res) => {
        if (res.code === 200) {
          this.setData({
            capture: `data:image/png;base64,${res.img}`,
            uuid: res.uuid,
          })
        }
      },
    })
  },

  // Handle relocation form input changes
  handleOriginCityInput(e) {
    this.setData({
      'relocationForm.originCity': e.detail.value,
    })
  },

  handleDestinationCityInput(e) {
    this.setData({
      'relocationForm.destinationCity': e.detail.value,
    })
  },

  handleDateChange(e) {
    const dateValue = e.detail.value
    // Convert YYYY-MM-DD to YYYY/MM/DD format
    const formattedDate = dateValue.replace(/-/g, '/')

    this.setData({
      'relocationForm.moveDate': formattedDate,
      'relocationForm.unknownDate': false,
    })
  },

  toggleUnknownDate() {
    const currentValue = this.data.relocationForm.unknownDate
    this.setData({
      'relocationForm.unknownDate': !currentValue,
    })
  },

  // Show form popup
  showFormPopup() {
    // Check if user is logged in
    const openId = wx.getStorageSync('token')
    if (!openId) {
      // If not logged in, show login prompt
      wx.showModal({
        title: '提示',
        content: '请先登录后再提交信息',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            // Redirect to login page or trigger login
            wx.navigateTo({
              url: '/pages/login/login',
            })
          }
        },
      })
      return
    }

    this.setData({
      showFormPopup: true,
    })
  },

  // Hide form popup
  hideFormPopup() {
    this.setData({
      showFormPopup: false,
    })
  },

  // Handle form submission
  formSubmit(e) {
    const formData = e.detail.value

    // Validate form data
    if (
      !formData.name ||
      !formData.phone ||
      !formData.company ||
      !formData.position ||
      !formData.email ||
      !formData.code
    ) {
      wx.showToast({
        title: '请填写必填项',
        icon: 'none',
        duration: 2000,
      })
      return
    }

    // Validate phone number format
    if (!/^1\d{10}$/.test(formData.phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none',
        duration: 2000,
      })
      return
    }

    // Validate email format
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      wx.showToast({
        title: '请输入正确的邮箱格式',
        icon: 'none',
        duration: 2000,
      })
      return
    }

    // Check if user has opted in for notifications
    const hasAcceptedNotifications = !!formData.acceptNotifications

    // Function to submit the form data
    const submitForm = () => {
      wx.showLoading({
        title: '提交中...',
        mask: true,
      })

      const userInfo = wx.getStorageSync('userInfo')
      http.request({
        url: `/api/wx/service/inquiry`,
        method: 'POST',
        data: {
          name: formData.name,
          phone: formData.phone,
          company: formData.company,
          position: formData.position,
          email: formData.email,
          remark: formData.remark,
          acceptNotifications: hasAcceptedNotifications,
          nickName: userInfo?.nickName || '',
          wxOpenId: wx.getStorageSync('token'),
          serviceType: this.data.pageTitle,
          code: formData.code,
          uuid: this.data.uuid,
          // Include relocation form data
          originCountry: this.data.relocationForm.originCountry,
          originCity: this.data.relocationForm.originCity,
          destinationCountry: this.data.relocationForm.destinationCountry,
          destinationCity: this.data.relocationForm.destinationCity,
          moveDate: this.data.relocationForm.moveDate,
          unknownDate: this.data.relocationForm.unknownDate,
        },
        success: (result) => {
          wx.hideLoading()
          if (result.code === 200) {
            wx.showToast({
              title: '提交成功',
              icon: 'success',
              duration: 2000,
            })
            // Hide the popup after successful submission
            this.hideFormPopup()
          } else {
            wx.showToast({
              title: result.msg || '验证码错误',
              icon: 'none',
              duration: 2000,
            })
            // Refresh captcha on error
            this.refreshCaptcha()
          }
        },
        fail: () => {
          wx.hideLoading()
          wx.showToast({
            title: '提交失败，请重试',
            icon: 'none',
            duration: 2000,
          })
          this.refreshCaptcha()
        },
      })
    }

    // If user has accepted notifications, request subscription permission
    if (hasAcceptedNotifications) {
      wx.requestSubscribeMessage({
        tmplIds: ['QPqVFUcr5976BAb_2OO6y_h_geKAiBlfjoT_ub4o5ww'],
        success: () => {
          submitForm()
        },
        fail: (err) => {
          console.error('订阅消息请求失败:', err)
          submitForm()
        },
      })
    } else {
      submitForm()
    }
  },
})
