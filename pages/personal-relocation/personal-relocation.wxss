/* pages/personal-relocation/personal-relocation.wxss */
.page-container {
  display: flex;
  flex-direction: column;
  background-color: #f2f5fb;
  min-height: 100vh;
  padding-bottom: 120rpx; /* Add padding for fixed button */
}

/* Header Styles */
.header {
  position: relative;
  height: 400rpx;
  width: 100%;
}

.header-image {
  width: 100%;
  height: 100%;
}

.header-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-content {
  text-align: center;
  color: white;
  padding: 0 40rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.header-description {
  font-size: 28rpx;
}

/* Section Styles */
.section {
  margin: 40rpx 30rpx;
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #cc0000;
  margin-bottom: 30rpx;
  text-align: center;
}

/* Services Grid */
.services-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background-color: #f9f9f9;
}

.service-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
  /* Icons would be added via background images or icon font */
}

.icon-home {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23cc0000"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-global {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23cc0000"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-diamond {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23cc0000"><path d="M12 3L2 12h3v8h6v-6h2v6h6v-8h3L12 3z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-box {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23cc0000"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 14h-2V9h-2V7h4v10z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.service-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.service-description {
  font-size: 24rpx;
  color: #666;
}

/* Content Block */
.content-block {
  padding: 20rpx;
}

.content-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  white-space: pre-line;
}

/* Fixed Button */
.fixed-button {
  position: fixed;
  bottom: 30rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: #cc0000;
  color: white;
  padding: 20rpx 60rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(204, 0, 0, 0.3);
  z-index: 10;
}

/* Popup Styles */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-content {
  width: 90%;
  max-height: 90vh;
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.popup-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #cc0000;
}

.popup-close {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
}

.popup-body {
  padding: 30rpx;
  overflow-y: auto;
  max-height: calc(90vh - 100rpx);
}

/* Relocation Form Styles */
.relocation-form {
  padding: 30rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  border: 1rpx solid #eee;
}

.form-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #cc0000;
  text-align: center;
  margin-bottom: 30rpx;
}

.country-select {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  margin-bottom: 10rpx;
  background-color: white;
}

.flag-container {
  display: flex;
  align-items: center;
}

.flag-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.select-arrow {
  font-size: 24rpx;
  color: #999;
}

.date-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  background-color: white;
}

.date-note {
  display: flex;
  align-items: center;
  height: 80rpx;
  color: #666;
  font-size: 28rpx;
  padding-left: 20rpx;
}

.next-button {
  background-color: #cc0000;
  color: white;
  width: 60%;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
}

/* Form Styles */
.form-row {
  display: flex;
  margin-bottom: 30rpx;
}

.form-item {
  flex: 1;
  margin: 0 10rpx;
}

.full-width {
  width: 100%;
}

.form-label {
  display: block;
  font-size: 28rpx;
  margin-bottom: 10rpx;
  color: #333;
}

.required {
  color: #cc0000;
  margin-left: 4rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  height: 160rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.captcha-container {
  display: flex;
  align-items: center;
}

.captcha-input {
  flex: 1;
}

.captcha-image {
  width: 200rpx;
  height: 80rpx;
  margin-left: 20rpx;
}

.checkbox-container {
  display: flex;
  align-items: center;
}

.checkbox-label {
  font-size: 28rpx;
  margin-left: 10rpx;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
}

.submit-button {
  background-color: #cc0000;
  color: white;
  width: 60%;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.block {
  background-color: var(--bg-color-demo);
  padding: 32rpx;
  margin: 32rpx 0 48rpx;
}
