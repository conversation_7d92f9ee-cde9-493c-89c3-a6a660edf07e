<!--pages/corporate-relocation/corporate-relocation.wxml-->
<view class="page-container">
  <!-- Header Section -->
  <view class="header">
    <image src="/images/1-24121P12S5B1.png" mode="aspectFill" class="header-image"></image>
    <view class="header-overlay">
      <view class="header-content">
        <text class="header-title">{{pageTitle}}</text>
        <text class="header-description">{{pageDescription}}</text>
      </view>
    </view>
  </view>

  <!-- Services Section -->
  <view class="section">
    <view class="relocation-form">
      <view class="form-title">安居咨询</view>
      <view class="form-container">
        <form bindsubmit="formSubmit">
          <!-- 详细信息 -->
          <view class="section-title">详细信息</view>

          <!-- 称呼、名字、姓氏 -->
          <view class="form-row">
            <view class="form-item">
              <text class="form-label">称呼<text class="required">*</text></text>
              <view class="external-class" bind:tap="showTitlePicker">
                <view class="picker-container">
                  <view class="picker-value">{{titles[selectedTitleIndex]}}</view>
                  <view class="picker-arrow">▼</view>
                </view>
              </view>
              <t-picker
                visible="{{titlePickerVisible}}"
                title="选择称呼"
                cancelBtn="取消"
                confirmBtn="确认"
                bindchange="onTitleChange"
                bindpick="onTitlePick"
                bindcancel="hideTitlePicker"
              >
                <t-picker-item options="{{titles}}" />
              </t-picker>
            </view>
            <view class="form-item">
              <text class="form-label">名字<text class="required">*</text></text>
              <t-input value="{{firstName}}" placeholder="请输入名字" bindchange="onFirstNameInput" />
            </view>
          </view>

          <view class="form-row">
            <view class="form-item">
              <text class="form-label">姓氏<text class="required">*</text></text>
              <t-input value="{{lastName}}" placeholder="请输入姓氏" bindchange="onLastNameInput" />
            </view>
            <view class="form-item">
              <text class="form-label">电子邮箱<text class="required">*</text></text>
              <t-input value="{{email}}" placeholder="请输入电子邮箱" bindchange="onEmailInput" type="text" />
            </view>
          </view>

          <view class="form-row">
            <view class="form-item">
              <text class="form-label">电话号码<text class="required">*</text></text>
              <t-input value="{{phone}}" placeholder="请输入电话号码" bindchange="onPhoneInput" type="number" />
            </view>
          </view>

          <!-- 协议和选项 -->
          <view class="form-row">
            <view class="form-item full-width">
              <view class="checkbox-item">
                <t-checkbox
                  value="{{privacyPolicyAgreed}}"
                  label="确认您已阅读并同意我们的隐私政策"
                  bind:change="onPrivacyPolicyChange"
                />
              </view>
              <view class="checkbox-item">
                <t-checkbox
                  value="{{marketingEmailsAgreed}}"
                  label="接收嘉柏发送的最新消息丶文章和优惠信息邮件"
                  bind:change="onMarketingEmailsChange"
                />
              </view>
            </view>
          </view>

          <!-- 提交按钮 -->
          <view class="form-actions">
            <t-button theme="primary" size="large" shape="round" block bind:tap="formSubmit">提交</t-button>
          </view>
        </form>
      </view>
    </view>
  </view>

  <!-- Introduction Section -->
  <view class="section">
    <view class="section-title">安居咨询</view>
    <view class="content-block">
      <text class="content-text">
        凭借处理全球和国内搬迁和流动计划的规模和能力，Crown World Mobility与管理其全球人才的公司合作。
        世界各地的组织都希望能够设计和实施量身定制的解决方案，以支持他们的员工搬迁，帮助他们享受并表现得更好——无论他们在哪里工作。
        我们的内部交付和选定的服务合作伙伴可以协调最简单的移动，以满足最复杂的移动需求。
      </text>
    </view>
  </view>
</view>
