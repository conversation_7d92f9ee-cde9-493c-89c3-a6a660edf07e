<!--pages/activity/activity.wxml-->
<view wx:if="{{!isLoading}}" class="activity-container">
  <image src="{{activityInfo.imgHeader}}" mode="widthFix" class="img" />
  <view class="invitation-card-container">
    <view class="invitation-card">
      <view wx:if="{{info.status !== '1'}}" class="invitation-text">
        <text wx:if="{{!info}}" class="hosts">{{activityInfo.activityContent}}</text>
        <view wx:else class="submitted-info">
          <view class="submitted-info-title">
            <text class="">{{info.name}}，您好</text>
            <text class="">感谢您的参与，期待与您相见</text>
          </view>
          <view wx:if="{{isCheckIn}}" class="submitted-info-content">
            <text class="">您的抽奖码是</text>
            <text class="lottery-number">{{info.lotteryNumber}}</text>
          </view>

          <view wx:else class="submitted-info-content">
            <text class="">以下为入场二维码</text>
            <image class="submitted-info-qrcode" src="{{qrCode}}" mode="aspectFit" />
            <text class="">请您在活动当天使用二维码签到</text>
          </view>
        </view>
        <view class="event-info-container">
          <view class="event-info-content">
            <text class="card-title">活动信息</text>
            <view class="event-details">
              <view class="event-details-table">
                <!-- Date row -->
                <view class="table-row">
                  <view class="label-cell">日期</view>
                  <view class="value-cell">{{activityInfo.activityDate}}</view>
                </view>

                <!-- Time row -->
                <view class="table-row">
                  <view class="label-cell">时间</view>
                  <view class="value-cell">{{activityInfo.startTime}} - {{activityInfo.endTime}}</view>
                </view>

                <!-- Venue row -->
                <view class="table-row">
                  <view class="label-cell">地点</view>
                  <view class="value-cell">{{activityInfo.location}}</view>
                </view>

                <!-- Address row -->
                <view class="table-row">
                  <view class="label-cell">地址</view>
                  <view class="value-cell">{{activityInfo.address}}</view>
                </view>

                <!-- Dress Code row -->
                <view class="table-row" wx:if="{{activityInfo.dress}}">
                  <view class="label-cell">着装</view>
                  <view class="value-cell">{{activityInfo.dress}}</view>
                </view>

                <!-- Footer row -->
                <view wx:if="{{activityInfo.remark}}" class="table-row">
                  <view class="full-width-cell">{{activityInfo.remark}}</view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view wx:if="{{info}}" class="event-info-container">
          <view class="event-info-content">
            <text class="card-title">活动流程</text>
            <view class="event-details">
              <view class="event-details-table">
                <!-- Date row -->
                <view wx:for="{{activityInfo.activityProcessList}}" wx:for-item="item" wx:key="index" class="table-row">
                  <view class="label-cell">{{item.time}}</view>
                  <view class="value-cell">{{item.content}}</view>
                </view>
              </view>
            </view>
          </view>
          <view class="event-info-content" style="margin-top: 24px">
            <text class="card-title">交通信息</text>
            <view class="event-details">
              <view class="traffic-info-card">
                <view class="traffic-section">
                  <view class="traffic-title">公共交通</view>
                  <view class="traffic-content">{{activityInfo.trafficPublic}}</view>
                </view>
                <view class="traffic-section">
                  <view class="traffic-title">打车</view>
                  <view class="traffic-content">{{activityInfo.trafficTaxi}}</view>
                </view>
                <view class="traffic-section">
                  <view class="traffic-title">自驾</view>
                  <view class="traffic-content">{{activityInfo.trafficDrive}}</view>
                </view>
              </view>
            </view>
          </view>
          <text class="event-cancel"
            >如您计划有变，请点击<text class="cancel-link" bindtap="cancelReservation">此处</text>取消预约</text
          >
        </view>
      </view>
      <view wx:else class="invitation-text cancel-text">
        <view class="cancel-header">
          <image class="cancel-icon" src="/assets/icons/cancel.png" mode="aspectFit" />
          <text class="cancel-title">预约已取消</text>
        </view>
        <view class="cancel-content">
          <text class="greeting">你好，{{info.name}}</text>
          <text class="message">很遗憾，您不能前往参加</text>
          <text class="activity-name">{{activityInfo.activityName}}</text>
          <text class="farewell">希望日后有机会与您相见</text>
        </view>
      </view>
    </view>
  </view>
  <image src="{{activityInfo.imgFooter}}" mode="widthFix" class="img img-footer" />

  <!-- Form Popup -->
  <view class="popup-mask" wx:if="{{showFormPopup}}">
    <view class="popup-content">
      <view class="popup-header">
        <text class="popup-title">{{info ? '报名信息' : '活动报名'}}</text>
        <text class="popup-close" bindtap="hideFormPopup">×</text>
      </view>
      <view class="popup-body">
        <!-- Show form for submission if user hasn't submitted yet -->
        <view class="form" wx:if="{{!info}}">
          <form bindsubmit="formSubmit">
            <view class="form-row">
              <view class="form-item">
                <text class="form-label">姓名<text class="required">*</text> </text>
                <input class="form-input" name="name" placeholder="请输入您的姓名" focus always-embed />
              </view>
              <view class="form-item">
                <text class="form-label">手机号<text class="required">*</text> </text>
                <input class="form-input" name="phone" type="number" placeholder="请输入您的手机号" always-embed />
              </view>
            </view>
            <view class="form-row">
              <view class="form-item">
                <text class="form-label">公司<text class="required">*</text> </text>
                <input class="form-input" name="company" placeholder="请输入您的公司名称" always-embed />
              </view>
              <view class="form-item">
                <text class="form-label">职位<text class="required">*</text> </text>
                <input class="form-input" name="title" placeholder="请输入您的职位" always-embed />
              </view>
            </view>
            <view class="form-row">
              <view class="form-item">
                <text class="form-label">邮箱<text class="required">*</text> </text>
                <input class="form-input" name="email" type="text" placeholder="请输入您的邮箱" always-embed />
              </view>
              <view class="form-item">
                <!-- Empty item to maintain the two-column layout -->
              </view>
            </view>
            <view class="form-row">
              <view class="form-item">
                <text class="form-label">验证码<text class="required">*</text> </text>
                <image class="captcha-image" src="{{capture}}" bindtap="refreshCaptcha" />
              </view>
              <view class="form-item">
                <text class="form-label" />
                <input class="form-input" name="code" type="number" placeholder="请输入验证码" always-embed />
              </view>
            </view>
            <button class="submit-btn" form-type="submit">提交</button>
          </form>
        </view>

        <!-- Show submitted information if user has already submitted -->
        <view class="submitted-info" wx:else>
          <view class="info-table">
            <view class="info-row">
              <view class="info-label">姓名:</view>
              <view class="info-value">{{info.name}}</view>
            </view>
            <view class="info-row">
              <view class="info-label">手机号:</view>
              <view class="info-value">{{info.phone}}</view>
            </view>
            <view class="info-row">
              <view class="info-label">公司:</view>
              <view class="info-value">{{info.company}}</view>
            </view>
            <view class="info-row">
              <view class="info-label">职位:</view>
              <view class="info-value">{{info.title}}</view>
            </view>
            <view class="info-row">
              <view class="info-label">邮箱:</view>
              <view class="info-value">{{info.email}}</view>
            </view>
          </view>
          <view class="submission-status">
            <text class="status-text">您已成功报名</text>
            <text class="status-time" wx:if="{{info.createTime}}">报名时间: {{info.createTime}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- Fixed Bottom Button -->
  <view wx:if="{{info.status !== '1'}}" class="fixed-bottom-button">
    <view class="button-container">
      <button wx:if="{{!info}}" class="action-btn submit-action" bindtap="showFormPopup">立即报名</button>
      <button wx:else class="action-btn view-action" bindtap="showFormPopup">查看报名信息</button>
    </view>
  </view>
</view>
