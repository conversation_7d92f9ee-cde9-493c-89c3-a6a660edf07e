/* pages/activity/activity.wxss */
.activity-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f2f5fb;
  padding-bottom: 80px;
  min-height: calc(100vh - 80px);
}

.img {
  width: 100%;
  height: auto;
}

.invitation-card-container {
  width: 100%;
  background-color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 20rpx;
}

.invitation-card {
  position: relative;
  width: 100%;
  background-color: white;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.submitted-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
  color: #cc9933;
}

.submitted-info-title {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.submitted-info-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.submitted-info-qrcode {
  width: 200rpx;
  height: 200rpx;
}

.geometric-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 400rpx;
  background:
    linear-gradient(135deg, #f0f0f0 25%, transparent 25%) -50px 0,
    linear-gradient(225deg, #f0f0f0 25%, transparent 25%) -50px 0,
    linear-gradient(315deg, #f0f0f0 25%, transparent 25%),
    linear-gradient(45deg, #f0f0f0 25%, transparent 25%);
  background-size: 100px 100px;
  background-color: #ffffff;
  opacity: 0.3;
  z-index: 0;
}

.anniversary-logo {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.crown-logo {
  font-size: 48rpx;
  font-weight: bold;
  color: #cc0000;
  letter-spacing: 4rpx;
}

.anniversary-number {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.number {
  font-size: 120rpx;
  font-weight: bold;
  color: #cc0000;
  line-height: 1;
}

.crown-icon {
  position: absolute;
  top: 10rpx;
  right: -20rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #ffcc00;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.crown-icon:before {
  content: '';
  width: 30rpx;
  height: 20rpx;
  background-color: #ffcc00;
  border-radius: 15rpx 15rpx 0 0;
}

.anniversary-text {
  font-size: 24rpx;
  color: #cc0000;
  margin-top: 10rpx;
  letter-spacing: 2rpx;
}

.invitation-text {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 40rpx;
  width: 100%;
}

.greeting {
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.hosts {
  font-size: 28rpx;
  padding: 0 40rpx;
  color: #333333;
  font-weight: 500;
  letter-spacing: 1rpx;
  line-height: 1.6;
  text-align: center;
  margin-bottom: 30rpx;
}

.event-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #cc9933;
  margin-bottom: 20rpx;
}

.event-date {
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.event-location,
.event-address {
  font-size: 26rpx;
  line-height: 1.4;
}

.event-details {
  margin: 0 20rpx;
}

/* Event details table styles */
.event-details-table {
  width: 100%;
  margin: 20rpx 0 30rpx;
  border: 1px solid #cc9933;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  background-color: #ffffff;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #cc9933;
}

.table-row:last-child {
  border-bottom: none;
}

.label-cell,
.value-cell {
  padding: 16rpx 20rpx;
  font-size: 26rpx;
  line-height: 1.4;
}

.label-cell {
  font-weight: bold;
  color: #cc9933;
  width: 30%;
  text-align: center;
  padding-right: 20rpx;
  border-right: 1px solid #cc9933;
}

.value-cell {
  flex: 1;
  color: #333;
  text-align: left;
}

.full-width-cell {
  font-size: small;
  width: 100%;
  text-align: center;
  padding: 20rpx;
  color: #666;
}

.rsvp-section {
  position: relative;
  z-index: 1;
  background-color: #cc0000;
  color: white;
  padding: 20rpx 60rpx;
  border-radius: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 10rpx rgba(204, 0, 0, 0.3);
}

.rsvp-text {
  font-size: 24rpx;
}

.rsvp-date {
  font-size: 28rpx;
  font-weight: bold;
}

.rsvp-confirmed {
  background-color: #4caf50;
}

.additional-info {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.info-text,
.dress-code {
  font-size: 24rpx;
  line-height: 1.5;
}

.social-icons {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: center;
  gap: 30rpx;
  margin-bottom: 20rpx;
}

.icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.copyright {
  position: relative;
  z-index: 1;
  font-size: 18rpx;
  color: #999;
  text-align: center;
}

/* Login Mask Styles */
.login-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.login-modal {
  width: 80%;
  max-width: 600rpx;
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
}

.login-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.login-modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.login-modal-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
  padding: 0 10rpx;
}

.login-modal-content {
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.login-modal-message {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  text-align: center;
}

.login-button {
  background-color: #cc0000;
  color: white;
  font-size: 28rpx;
  padding: 20rpx 60rpx;
  border-radius: 40rpx;
  border: none;
  box-shadow: 0 4rpx 10rpx rgba(204, 0, 0, 0.3);
}

/* Form Styles */
.form {
  width: 100%;
  margin: 20rpx 0 30rpx;
}

.form-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  width: 100%;
}

.form-item {
  margin-bottom: 20rpx;
  width: 48%; /* Slightly less than 50% to allow for spacing */
}

.form-item.full-width {
  width: 100%;
}

.form-label {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-weight: bold;
  height: 20px;
}

.required {
  color: #cc0000;
  margin-left: 4rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  background-color: #f9f9f9;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  height: 160rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 26rpx;
  background-color: #f9f9f9;
  box-sizing: border-box;
}

.captcha-image {
  width: 100%;
  height: 80rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  background-color: #f9f9f9;
  box-sizing: border-box;
}

.notification-container {
  padding: 20rpx 0;
}

.notification-checkbox {
  margin: 20rpx 0;
  border-radius: 8rpx;
}

.checkbox-label {
  display: flex;
  align-items: center;
  font-size: 18rpx;
  color: #666;
}

.checkbox-text {
  margin-left: 8rpx;
}

.tip-text {
  margin-left: 8rpx;
  color: #999;
}

.submit-btn {
  background: linear-gradient(135deg, #cc0000, #e60000);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 48rpx;
  border: none;
  box-shadow: 0 6rpx 20rpx rgba(204, 0, 0, 0.3);
  width: 100%;
  margin-top: 30rpx;
  letter-spacing: 4rpx;
}

/* Register Button */
.register-btn {
  background: linear-gradient(135deg, #cc0000, #e60000);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 48rpx;
  border: none;
  box-shadow: 0 6rpx 20rpx rgba(204, 0, 0, 0.3);
  width: 80%;
  margin: 30rpx auto;
  letter-spacing: 4rpx;
}

/* Popup Styles */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.popup-content {
  width: 90%;
  max-width: 650rpx;
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.popup-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
  padding: 0 10rpx;
}

.popup-body {
  padding: 30rpx;
  overflow-y: auto;
}

/* Fixed Bottom Button Styles */
.fixed-bottom-button {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.98);
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);
  z-index: 100;
  display: flex;
  justify-content: center;
  backdrop-filter: blur(10px);
  height: 80px;
}

.button-container {
  padding: 24rpx 40rpx 40rpx;
  width: 100%;
}

.action-btn {
  text-align: center;
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  border: none;
  letter-spacing: 4rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-btn::after {
  content: '';
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.3) 10%, transparent 10.01%);
  background-repeat: no-repeat;
  background-position: 50%;
  transform: scale(10, 10);
  opacity: 0;
  transition:
    transform 0.5s,
    opacity 1s;
}

.action-btn:active::after {
  transform: scale(0, 0);
  opacity: 0.3;
  transition: 0s;
}

.submit-action {
  background: linear-gradient(135deg, #cc0000, #e60000);
  box-shadow: 0 6rpx 20rpx rgba(204, 0, 0, 0.3);
}

.view-action {
  /* background: linear-gradient(135deg, #cc9933, #e6b142); */
  background: linear-gradient(135deg, #cc0000, #e60000);
  box-shadow: 0 6rpx 20rpx rgba(204, 153, 51, 0.3);
}

/* Submitted Information Styles */
.submitted-info {
  width: 100%;
}

.info-table {
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
  background-color: #ffffff;
}

.info-row {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  width: 30%;
  padding: 20rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #cc9933;
  background-color: #f9f9f9;
  border-right: 1px solid #e0e0e0;
  text-align: right;
}

.info-value {
  flex: 1;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
}

.submission-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 30rpx;
}

.status-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #4caf50;
  margin-bottom: 10rpx;
}

.status-time {
  font-size: 24rpx;
  color: #999;
}

.event-info-container {
  width: 100%;
}

.event-info-content {
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  margin: 24rpx;
  padding: 20rpx;
  border: 1px solid rgba(204, 153, 51, 0.1);
}

.event-cancel {
  font-size: 24rpx;
  font-weight: bold;
  color: #cc9933;
  margin-bottom: 20rpx;
  width: 100%;
}

.cancel-link {
  color: #d19a2b;
  text-decoration: underline;
  cursor: pointer;
}

.cancel-text {
  padding: 40rpx 30rpx;
  background: #fff;
  margin: 30rpx;
}

.cancel-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.cancel-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.cancel-title {
  font-size: 36rpx;
  color: #cc0000;
  font-weight: bold;
}

.cancel-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.cancel-content .greeting {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.cancel-content .message {
  font-size: 28rpx;
  color: #666;
}

.cancel-content .activity-name {
  font-size: 32rpx;
  color: #cc9933;
  font-weight: bold;
  margin: 10rpx 0;
}

.cancel-content .farewell {
  font-size: 28rpx;
  color: #666;
  margin-top: 10rpx;
}

.traffic-info-card {
  padding: 20rpx 24rpx;
  margin: 20rpx 0;
  text-align: left;
  color: #a87b2a;
  font-size: 28rpx;
  display: flex;
  flex-direction: column;
}

.traffic-section {
  margin-bottom: 32rpx;
}

.traffic-title {
  font-weight: bold;
  font-size: 30rpx;
  margin-bottom: 8rpx;
}

.traffic-content {
  font-size: 24rpx;
  line-height: 1.7;
  word-break: break-all;
  color: #333;
}

.traffic-section:last-child {
  margin-bottom: 0;
}

.small-checkbox {
  transform: scale(0.8);
  margin-right: 4rpx;
}

.card-title {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #cc9933;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 20rpx 20rpx;
  padding: 18rpx 0;
  margin-bottom: 32rpx;
  width: 80%;
  margin-left: auto;
  margin-right: auto;
  letter-spacing: 2rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}

.lottery-number {
  font-size: 48px;
  font-weight: bold;
  color: #cc0000;
}
