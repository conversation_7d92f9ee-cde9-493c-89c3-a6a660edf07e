// pages/activity/activity.js
import { http, URL } from '../../utils/http.js'

let timer = null

Page({
  data: {
    activityId: '',
    openId: '',
    info: {},
    showFormPopup: false,
    capture: '',
    uuid: '',
    activityInfo: {},
    isLoading: true,
    qrCode: null,
    isCheckIn: false,
  },

  onLoad(option) {
    const scene = decodeURIComponent(option.scene)
    const activityId = scene.split('=')[1]

    const app = getApp()
    app
      .checkLogin()
      .then((openId) => {
        this.setData({
          activityId: activityId,
          openId,
        })
        wx.showLoading({
          title: '加载中...',
          mask: true,
        })

        this.loadUserInfo((res) => {
          this.loadingActivity(!!res.data)

          const isCheckIn = res.data?.checkIn === 'Y'
          // 已报名且未签到
          if (res.data?.status === '0' && !isCheckIn) {
            this.getQrCode()
          }
        })
      })
      .catch((err) => {
        wx.showToast({
          title: '获取用户信息失败，请重试',
          icon: 'none',
          duration: 2000,
        })
      })
  },

  loadUserInfo(callback) {
    http.request({
      url: `/api/wx/activity/${this.data.activityId}/participant/${this.data.openId}`,
      success: (res) => {
        timer && clearInterval(timer)

        // Format create time if it exists
        if (res.data?.createTime) {
          const formatTime = (timeStr) => {
            if (!timeStr) return timeStr
            return timeStr.split(':').slice(0, 2).join(':')
          }
          res.data.createTime = formatTime(res.data.createTime)
        }

        const isCheckIn = res.data?.checkIn === 'Y'

        this.setData({
          info: res.data,
          isCheckIn,
        })

        if (!res.data) {
          this.refreshCaptcha()
        }

        // 已报名且未签到
        if (res.data?.status === '0' && !isCheckIn) {
          timer = setInterval(() => {
            this.loadUserInfo()
          }, 5000)
        }

        if (callback) {
          callback(res)
        }

        wx.hideLoading()
      },
    })
  },

  getQrCode() {
    http.request({
      url: `/api/wx/activity/qrcode/checkIn?activityId=${this.data.activityId}&openId=${this.data.openId}`,
      success: (res) => {
        if (res.code === 200) {
          this.setData({ qrCode: `data:image/png;base64,${res.data}` })
        }
      },
    })
  },

  refreshCaptcha() {
    http.request({
      url: `/api/wx/captchaImage`,
      success: (res) => {
        if (res.code === 200) {
          this.setData({
            capture: `data:image/png;base64,${res.img}`,
            uuid: res.uuid,
          })
        }
      },
    })
  },

  loadingActivity(isSubmit) {
    http.request({
      url: `/api/wx/${this.data.activityId}`,
      success: (res) => {
        const data = isSubmit ? res.data.activityProcess : res.data.activityContent

        // Format time to remove seconds
        const formatTime = (timeStr) => {
          if (!timeStr) return timeStr
          return timeStr.split(':').slice(0, 2).join(':')
        }

        // Format activity process times
        const processList = res.data.activityProcessList?.map((item) => ({
          ...item,
          time: formatTime(item.time),
        }))

        this.setData({
          activityInfo: {
            ...res.data,
            imgHeader: `${URL}${res.data.imgHeader}`,
            imgFooter: `${URL}${res.data.imgFooter}`,
            startTime: formatTime(res.data.startTime),
            endTime: formatTime(res.data.endTime),
            activityProcessList: processList,
          },
          isLoading: false,
        })
      },
    })
  },

  // Show form popup
  showFormPopup() {
    // Check if user is logged in
    const openId = wx.getStorageSync('token')
    if (!openId) {
      // If not logged in, show login prompt
      wx.showModal({
        title: '提示',
        content: '请先登录后再进行报名',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            // Redirect to login page or trigger login
            wx.navigateTo({
              url: '/pages/login/login',
            })
          }
        },
      })
      return
    }

    // If user has already submitted form (info exists), show the form with their data
    // Otherwise show empty form for submission
    this.setData({
      showFormPopup: true,
    })
  },

  // Hide form popup
  hideFormPopup() {
    this.setData({
      showFormPopup: false,
    })
  },

  // Handle form submission
  formSubmit(e) {
    const formData = e.detail.value

    // Validate form data
    if (
      !formData.name ||
      !formData.phone ||
      !formData.company ||
      !formData.title ||
      !formData.email ||
      !formData.code
    ) {
      wx.showToast({
        title: '请填写必填项',
        icon: 'none',
        duration: 2000,
      })
      return
    }

    // Validate phone number format (simple validation)
    if (!/^1\d{10}$/.test(formData.phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none',
        duration: 2000,
      })
      return
    }

    // Validate email format (simple validation)
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      wx.showToast({
        title: '请输入正确的邮箱格式',
        icon: 'none',
        duration: 2000,
      })
      return
    }

    // Function to submit the form data
    const submitForm = () => {
      wx.showLoading({
        title: '提交中...',
        mask: true,
      })

      const userInfo = wx.getStorageSync('userInfo')
      http.request({
        url: `/api/wx/activity/participant`,
        method: 'POST',
        data: {
          name: formData.name,
          phone: formData.phone,
          company: formData.company,
          title: formData.title,
          email: formData.email,
          nickName: userInfo.nickName,
          wxOpenId: this.data.openId,
          activityId: this.data.activityId,
          code: formData.code,
          uuid: this.data.uuid,
          acceptNotice: 'Y',
        },
        success: (result) => {
          wx.hideLoading()
          if (result.code === 200) {
            wx.showToast({
              title: '提交成功',
              icon: 'success',
              duration: 2000,
            })
            // Hide the popup after successful submission
            this.hideFormPopup()
            // Reload user info
            this.loadUserInfo()
            this.getQrCode()
          } else {
            wx.showToast({
              title: result.msg || '验证码错误',
              icon: 'none',
              duration: 2000,
            })
            // Refresh captcha on error
            this.refreshCaptcha()
          }
        },
        fail: () => {
          wx.hideLoading()
          wx.showToast({
            title: '提交失败，请重试',
            icon: 'none',
            duration: 2000,
          })
          this.refreshCaptcha()
        },
      })
    }

    // 模板消息类型可以包括：活动提醒、活动变更通知、签到成功通知等
    // 详细文档：https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/subscribe-message.html
    wx.requestSubscribeMessage({
      tmplIds: ['4vz8LMfRaR5lSmu7F69ri4o_u5nueGDV0wsYyvVvgSc'],
      success: (res) => {
        // TODO: 后端提供字段
        // 继续提交表单
        submitForm()
      },
      fail: (err) => {
        console.error('订阅消息请求失败:', err)
        // 即使订阅消息请求失败，也继续提交表单
        submitForm()
      },
    })
  },

  cancelReservation() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消预约吗？取消后将无法恢复。',
      confirmText: '确定取消',
      cancelText: '再想想',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '取消中...' })
          http.request({
            url: `/api/wx/activity/${this.data.activityId}/participant/${this.data.openId}`,
            method: 'post',
            success: (res) => {
              wx.hideLoading()
              if (res.code === 200) {
                wx.showToast({ title: '已取消预约', icon: 'success' })
                this.loadUserInfo()
              } else {
                wx.showToast({ title: res.msg, icon: 'none' })
              }
            },
          })
        }
      },
    })
  },
})
