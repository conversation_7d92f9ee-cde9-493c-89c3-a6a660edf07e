// app.js
import { http } from './utils/http.js'

App({
  onLaunch() {
    wx.getUserInfo({
      success: (res) => {
        wx.setStorageSync('userInfo', res.userInfo)
      },
    })

    this.checkLogin()
  },

  // 添加登录状态管理
  _loginPromise: null,

  checkLogin() {
    // 如果已经有正在进行的登录请求，直接返回该Promise
    if (this._loginPromise) {
      return this._loginPromise
    }

    // 创建新的登录请求
    this._loginPromise = new Promise((resolve, reject) => {
      // 登录
      wx.login({
        success: (res) => {
          http.request({
            url: '/api/wx/user/uni-info',
            data: {
              code: res.code,
            },
            success: (res) => {
              wx.setStorageSync('token', res.data.openId)
              resolve(res.data.openId)
            },
            fail: (err) => {
              reject(err)
            },
            complete: () => {
              // 请求完成后清除Promise引用
              this._loginPromise = null
            },
          })
        },
        fail: (err) => {
          this._loginPromise = null
          reject(err)
        },
      })
    })

    return this._loginPromise
  },
})
